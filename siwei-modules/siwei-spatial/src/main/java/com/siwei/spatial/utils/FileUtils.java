package com.siwei.spatial.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 文件工具类
 */
public class FileUtils {
    /**
     * 解压缩zip文件到指定目录
     *
     * @param zipFilePath
     * @param destDir
     * @throws IOException
     */
    public static void unzipAutoCharset(String zipFilePath, String destDir) throws IOException {
        try {
            // 先尝试 UTF-8
            unzipWithCharset(zipFilePath, destDir, Charset.forName("UTF-8"));
        } catch (IllegalArgumentException | java.nio.charset.MalformedInputException e) {
            // 如果失败，切换到 GBK
            unzipWithCharset(zipFilePath, destDir, Charset.forName("GBK"));
        }
    }

    /**
     * 解压缩zip文件到指定目录，使用指定字符集
     *
     * @param zipFilePath
     * @param destDir
     * @param charset
     * @throws IOException
     */
    private static void unzipWithCharset(String zipFilePath, String destDir, Charset charset) throws IOException {
        File dir = new File(destDir);
        if (!dir.exists()) dir.mkdirs();

        try (ZipFile zipFile = new ZipFile(zipFilePath, charset)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                File newFile = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    new File(newFile.getParent()).mkdirs();
                    try (InputStream is = zipFile.getInputStream(entry); FileOutputStream fos = new FileOutputStream(newFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }

    /**
     * 读取文件夹中及子文件夹中的所有shp相关文件（.shp,.dbf,.prj,.qix,.shx），返回map结构，后缀名为key，文件路径为value
     *
     * @param dirPath 文件夹路径
     */
    public static List<Map<String, String>> readShpFiles(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            throw new IllegalArgumentException("Provided path is not a valid directory: " + dirPath);
        }
        Map<String, Map<String, String>> groupedFiles = new HashMap<>();
        readShpFilesRecursively(dir, groupedFiles);
        return new ArrayList<>(groupedFiles.values());
    }

    /**
     * 递归读取目录中的shp相关文件，并将它们按文件名分组
     *
     * @param dir
     * @param groupedFiles
     */
    private static void readShpFilesRecursively(File dir, Map<String, Map<String, String>> groupedFiles) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    readShpFilesRecursively(file, groupedFiles);
                } else {
                    String fileName = file.getName();
                    String lowerName = fileName.toLowerCase();
                    if (lowerName.endsWith(".shp") || lowerName.endsWith(".dbf") || lowerName.endsWith(".prj") || lowerName.endsWith(".qix") || lowerName.endsWith(".shx")) {
                        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                        String ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
                        groupedFiles.computeIfAbsent(baseName, k -> new HashMap<>()).put(ext, file.getAbsolutePath());
                    }
                }
            }
        }
    }
}