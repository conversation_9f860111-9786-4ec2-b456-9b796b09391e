package com.siwei.spatial.controller.file;

import com.siwei.common.core.domain.R;
import com.siwei.common.core.utils.StringUtils;
import com.siwei.common.core.web.controller.BaseController;
import com.siwei.common.security.annotation.InnerAuth;
import com.siwei.spatial.api.domain.file.TGeomDb;
import com.siwei.spatial.api.domain.file.TGeomDbDetails;
import com.siwei.spatial.service.file.ISpatialFilesDbService;
import com.siwei.spatial.service.file.ITGeomDbDetailsService;
import com.siwei.spatial.service.file.ITGeomDbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * 空间与shp文件入库相关操作处理
 *
 * <br>
 * 所有接口先声明为内部接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/files/db")
public class SpatialFilesDbController extends BaseController {

    @Autowired
    private ISpatialFilesDbService iSpatialFilesDbService;
    @Autowired
    private ITGeomDbDetailsService itGeomDbDetailsService;
    @Autowired
    private ITGeomDbService itGeomDbService;

    @PostMapping("/read/shp")
    // @InnerAuth
    public R<TGeomDb> readShp(String shpFileName, String shpFilePath, String fromRoute) throws IOException {
        return R.ok(iSpatialFilesDbService.readShpFile(shpFileName, shpFilePath, fromRoute));
    }

    @PostMapping("/read/wkt")
    @InnerAuth
    public R<TGeomDb> readWkt(String shpFileName, String wkt, String fromRoute) {
        return R.ok(iSpatialFilesDbService.readGeom(shpFileName, wkt, fromRoute));
    }

    @PostMapping("/read/geom")
    @InnerAuth
    public R<TGeomDb> readWkt(String shpFileName, String geom, String fromRoute, String fromType) throws IOException {
        if (StringUtils.isEmpty(fromType)) {
            return R.fail(-1, "执行失败，空间类型为空");
        }
        if (StringUtils.isEmpty(fromType)) {
            return R.fail(-1, "执行失败，空间信息为空");
        }
        if ("1".equals(fromType)) {
            return R.ok(iSpatialFilesDbService.readShpFile(shpFileName, geom, fromRoute));
        } else if ("2".equals(fromType)) {
            return R.ok(iSpatialFilesDbService.readGeom(shpFileName, geom, fromRoute));
        } else {
            return R.fail(-1, "执行失败，空间类型不正确");
        }
    }

    /**
     * 根据ID查询空间信息，
     *
     * @param id
     * @param isDetail 是否查询详情，1是 其他否
     * @return
     * @throws Exception
     */
    @GetMapping("/query/geom")
    @InnerAuth
    public R<TGeomDb> queryGeom(String id, Integer isDetail) {
        if (StringUtils.isEmpty(id)) {
            return R.fail(-1, "执行失败，id参数缺失");
        }
        TGeomDb geomDb = itGeomDbService.selectTGeomDbById(id);
        if (null != geomDb && null != isDetail && isDetail == 1) {
            TGeomDbDetails tGeomDbDetails = new TGeomDbDetails();
            tGeomDbDetails.setUploadId(id);
            geomDb.setDetails(itGeomDbDetailsService.selectTGeomDbDetailsList(tGeomDbDetails));
        }
        return R.ok(geomDb);
    }

    @GetMapping("/query/geom/details")
    @InnerAuth
    public R<List<TGeomDbDetails>> queryGeomDetails(String id) {
        if (StringUtils.isEmpty(id)) {
            return R.fail(-1, "执行失败，id参数缺失");
        }
        TGeomDbDetails tGeomDbDetails = new TGeomDbDetails();
        tGeomDbDetails.setUploadId(id);
        return R.ok(itGeomDbDetailsService.selectTGeomDbDetailsList(tGeomDbDetails));
    }

    @GetMapping("/query/geom/details/page")
    @InnerAuth
    public R<List<TGeomDbDetails>> queryGeomDetailsPage(String id) {
        if (StringUtils.isEmpty(id)) {
            return R.fail(-1, "执行失败，id参数缺失");
        }
        startPage();
        TGeomDbDetails tGeomDbDetails = new TGeomDbDetails();
        tGeomDbDetails.setUploadId(id);
        List<TGeomDbDetails> list = itGeomDbDetailsService.selectTGeomDbDetailsList(tGeomDbDetails);
        List<TGeomDbDetails> list2 = (List<TGeomDbDetails>) getDataTable(list).getRows();
        return R.ok(list2);
    }


}
