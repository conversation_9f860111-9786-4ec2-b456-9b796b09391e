<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.JsgcghxkMapper">

    <!-- ResultMap 映射 -->
    <resultMap id="JsgcghxkResultMap" type="com.siwei.apply.domain.Jsgcghxk">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="ydwz" column="ydwz"/>
        <result property="qs" column="qs"/>
        <result property="djq" column="djq"/>
        <result property="jsgm" column="jsgm"/>
        <result property="zsbh" column="zsbh"/>
        <result property="fzjg" column="fzjg"/>
        <result property="fzDate" column="fz_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_jsgcghxk
        WHERE project_id = #{projectId}
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.siwei.apply.domain.Jsgcghxk">
        INSERT INTO t_jsgcghxk (id, project_id, ydwz, qs, djq, jsgm, zsbh, fzjg, fz_date, has_onchain,
                                creator_id, created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{ydwz}, #{qs}, #{djq}, #{jsgm}, #{zsbh}, #{fzjg}, #{fzDate},
                false, #{creatorId}, now(), now())
    </insert>

    <!-- 根据项目ID获取记录 -->
    <select id="get" resultMap="JsgcghxkResultMap" parameterType="String">
        SELECT *
        FROM t_jsgcghxk
        WHERE project_id = #{projectId}
        ORDER BY created_at ASC
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.JsgcghxkUpdateVo">
        UPDATE t_jsgcghxk
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="ydwz != null">ydwz = #{ydwz},</if>
            <if test="qs != null">qs = #{qs},</if>
            <if test="djq != null">djq = #{djq},</if>
            <if test="jsgm != null">jsgm = #{jsgm},</if>
            <if test="zsbh != null">zsbh = #{zsbh},</if>
            <if test="fzjg != null">fzjg = #{fzjg},</if>
            <if test="fzDate != null">fz_date = #{fzDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

</mapper>