<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.NodeLandMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siwei.apply.domain.NodeLand">
        <id property="id" column="id"/>
        <result property="nodeId" column="node_id"/>
        <result property="geomDbId" column="geom_db_id"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, node_id, geom_db_id
    </sql>

    <!-- 根据node_id查询地块几何信息，返回ewkt格式 -->
    <select id="selectGeomByNodeId" resultType="String" parameterType="String">
        SELECT
            ST_AsEWKT(gd.geom) as geom
        FROM t_node_land nl
        LEFT JOIN t_geom_db_details gd ON nl.geom_db_id = gd.upload_id
        WHERE nl.node_id = #{nodeId}
        AND gd.geom IS NOT NULL
        ORDER BY nl.id
    </select>

    <!-- 根据node_id和geom_db_id创建记录 -->
    <insert id="insertByNodeIdAndGeomDbId">
        INSERT INTO t_node_land (
            id,
            node_id,
            geom_db_id
        ) VALUES (
            gen_random_uuid()::varchar,
            #{param1},
            #{param2}
        )
    </insert>

    <!-- 根据node_id删除记录 -->
    <delete id="deleteByNodeId" parameterType="String">
        DELETE FROM t_node_land
        WHERE node_id = #{nodeId}
    </delete>

</mapper>
