<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.NodeLandMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siwei.apply.domain.NodeLand">
        <id property="id" column="id"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeTable" column="node_table"/>
        <result property="projectId" column="project_id"/>
        <result property="geomDbDetailsId" column="geom_db_details_id"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, node_id, node_table, project_id, geom_db_details_id
    </sql>

    <!-- 暂时不添加具体的SQL语句 -->
    <!-- 后续根据业务需要再添加相应的查询、插入、更新、删除语句 -->

</mapper>
