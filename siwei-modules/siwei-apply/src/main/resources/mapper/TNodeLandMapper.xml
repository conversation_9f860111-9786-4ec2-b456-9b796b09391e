<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.TNodeLandMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siwei.apply.domain.TNodeLand">
        <id property="id" column="id"/>
        <result property="nodeId" column="node_id"/>
        <result property="nodeTable" column="node_table"/>
        <result property="projectId" column="project_id"/>
        <result property="geomDbDetailsId" column="geom_db_details_id"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, node_id, node_table, project_id, geom_db_details_id
    </sql>

    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="String">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_node_land
        WHERE id = #{id}
    </select>

    <!-- 根据项目ID查询记录列表 -->
    <select id="selectByProjectId" resultMap="BaseResultMap" parameterType="String">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_node_land
        WHERE project_id = #{projectId}
        ORDER BY id
    </select>

    <!-- 根据节点ID和节点表名查询记录 -->
    <select id="selectByNodeIdAndTable" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_node_land
        WHERE node_id = #{nodeId} AND node_table = #{nodeTable}
        ORDER BY id
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.siwei.apply.domain.TNodeLand">
        INSERT INTO t_node_land (
            id, 
            node_id, 
            node_table, 
            project_id, 
            geom_db_details_id
        ) VALUES (
            #{id}, 
            #{nodeId}, 
            #{nodeTable}, 
            #{projectId}, 
            #{geomDbDetailsId}
        )
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.TNodeLand">
        UPDATE t_node_land
        <set>
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="nodeTable != null">node_table = #{nodeTable},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="geomDbDetailsId != null">geom_db_details_id = #{geomDbDetailsId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteById" parameterType="String">
        DELETE FROM t_node_land WHERE id = #{id}
    </delete>

    <!-- 根据项目ID删除记录 -->
    <delete id="deleteByProjectId" parameterType="String">
        DELETE FROM t_node_land WHERE project_id = #{projectId}
    </delete>

</mapper>
