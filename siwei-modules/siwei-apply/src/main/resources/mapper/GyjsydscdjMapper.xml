<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siwei.apply.mapper.GyjsydscdjMapper">

    <resultMap id="GyjsydscdjResultMap" type="com.siwei.apply.domain.Gyjsydscdj">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="qlr" column="qlr"/>
        <result property="gyqk" column="gyqk"/>
        <result property="zl" column="zl"/>
        <result property="qllx" column="qllx"/>
        <result property="qlxz" column="qlxz"/>
        <result property="yt" column="yt"/>
        <result property="area" column="area"/>
        <result property="bdcdyh" column="bdcdyh"/>
        <result property="bdczh" column="bdczh"/>
        <result property="djjg" column="djjg"/>
        <result property="djDate" column="dj_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_gyjsydscdj
        WHERE project_id = #{projectId}
    </select>

    <insert id="add" parameterType="com.siwei.apply.domain.Gyjsydscdj">
        INSERT INTO t_gyjsydscdj (id, project_id, qlr, gyqk, zl, qllx, qlxz, yt, area, bdcdyh, bdczh, djjg, dj_date,
                                  has_onchain, creator_id, created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{qlr}, #{gyqk}, #{zl}, #{qllx}, #{qlxz}, #{yt}, #{area}, #{bdcdyh}, #{bdczh},
                #{djjg}, #{djDate},
                #{hasOnchain}, #{creatorId}, now(), now())
    </insert>

    <!-- 根据项目ID获取记录 -->
    <select id="get" resultMap="GyjsydscdjResultMap" parameterType="String">
        SELECT *
        FROM t_gyjsydscdj
        WHERE project_id = #{projectId}
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.GyjsydscdjUpdateVo">
        UPDATE t_gyjsydscdj
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="qlr != null">qlr = #{qlr},</if>
            <if test="gyqk != null">gyqk = #{gyqk},</if>
            <if test="zl != null">zl = #{zl},</if>
            <if test="qllx != null">qllx = #{qllx},</if>
            <if test="qlxz != null">qlxz = #{qlxz},</if>
            <if test="yt != null">yt = #{yt},</if>
            <if test="area != null">area = #{area},</if>
            <if test="bdcdyh != null">bdcdyh = #{bdcdyh},</if>
            <if test="bdczh != null">bdczh = #{bdczh},</if>
            <if test="djjg != null">djjg = #{djjg},</if>
            <if test="djDate != null">dj_date = #{djDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>
</mapper>