<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.TdgyMapper">

    <resultMap id="BaseResultMap" type="com.siwei.apply.domain.Tdgy">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="srf" column="srf"/>
        <result property="tdyt" column="tdyt"/>
        <result property="jswz" column="jswz"/>
        <result property="gdArea" column="gd_area"/>
        <result property="gdType" column="gd_type"/>
        <result property="hasZz" column="has_zz"/>
        <result property="hbcrfapfwh" column="hbcrfapfwh"/>
        <result property="hbcrhtbh" column="hbcrhtbh"/>
        <result property="hbcrhtDate" column="hbcrht_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_tdgy
        WHERE project_id = #{projectId}
    </select>

    <!-- 添加记录 -->
    <insert id="add" parameterType="com.siwei.apply.domain.Tdgy">
        INSERT INTO t_tdgy (id, project_id, srf, tdyt, jswz,
                            gd_area, gd_type, has_zz, hbcrfapfwh, hbcrhtbh,
                            hbcrht_date, has_onchain, creator_id,
                            created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{srf}, #{tdyt}, #{jswz},
                #{gdArea}, #{gdType}, #{hasZz}, #{hbcrfapfwh}, #{hbcrhtbh},
                #{hbcrhtDate}, false, #{creatorId},
                now(), now())
    </insert>

    <!-- 根据项目ID获取记录 -->
    <select id="get" resultMap="BaseResultMap" parameterType="String">
        SELECT *
        FROM t_tdgy
        WHERE project_id = #{projectId}
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.TdgyUpdateVo">
        UPDATE t_tdgy
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="srf != null">srf = #{srf},</if>
            <if test="tdyt != null">tdyt = #{tdyt},</if>
            <if test="jswz != null">jswz = #{jswz},</if>
            <if test="gdArea != null">gd_area = #{gdArea},</if>
            <if test="gdType != null">gd_type = #{gdType},</if>
            <if test="hasZz != null">has_zz = #{hasZz},</if>
            <if test="hbcrfapfwh != null">hbcrfapfwh = #{hbcrfapfwh},</if>
            <if test="hbcrhtbh != null">hbcrhtbh = #{hbcrhtbh},</if>
            <if test="hbcrhtDate != null">hbcrht_date = #{hbcrhtDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

</mapper>
