<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.JsydghxkMapper">

    <resultMap id="BaseResultMap" type="com.siwei.apply.domain.Jsydghxk">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="yddw" column="yddw"/>
        <result property="ydwz" column="ydwz"/>
        <result property="ydArea" column="yd_area"/>
        <result property="tdyt" column="tdyt"/>
        <result property="tdhqfs" column="tdhqfs"/>
        <result property="pzydjg" column="pzydjg"/>
        <result property="pzydwh" column="pzydwh"/>
        <result property="jsgm" column="jsgm"/>
        <result property="zsbh" column="zsbh"/>
        <result property="fzjg" column="fzjg"/>
        <result property="fzDate" column="fz_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_jsydghxk
        WHERE project_id = #{projectId}
    </select>

    <!-- 添加记录 -->
    <insert id="add" parameterType="com.siwei.apply.domain.Jsydghxk">
        INSERT INTO t_jsydghxk (id, project_id, yddw, ydwz, yd_area, tdyt, tdhqfs, pzydjg, pzydwh,
                                jsgm, zsbh, fzjg, fz_date, has_onchain, creator_id, created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{yddw}, #{ydwz}, #{ydArea}, #{tdyt}, #{tdhqfs}, #{pzydjg}, #{pzydwh},
                #{jsgm}, #{zsbh}, #{fzjg}, #{fzDate}, false, #{creatorId}, now(), now())
    </insert>

    <!-- 根据项目ID获取记录 -->
    <select id="get" resultMap="BaseResultMap" parameterType="String">
        SELECT *
        FROM t_jsydghxk
        WHERE project_id = #{projectId}
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.JsydghxkUpdateVo">
        UPDATE t_jsydghxk
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="yddw != null">yddw = #{yddw},</if>
            <if test="ydwz != null">ydwz = #{ydwz},</if>
            <if test="ydArea != null">yd_area = #{ydArea},</if>
            <if test="tdyt != null">tdyt = #{tdyt},</if>
            <if test="tdhqfs != null">tdhqfs = #{tdhqfs},</if>
            <if test="pzydjg != null">pzydjg = #{pzydjg},</if>
            <if test="pzydwh != null">pzydwh = #{pzydwh},</if>
            <if test="jsgm != null">jsgm = #{jsgm},</if>
            <if test="zsbh != null">zsbh = #{zsbh},</if>
            <if test="fzjg != null">fzjg = #{fzjg},</if>
            <if test="fzDate != null">fz_date = #{fzDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

</mapper>