<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.siwei.apply.mapper.TjyydhxMapper">

    <resultMap id="TjyydhxResultMap" type="com.siwei.apply.domain.Tjyydhx">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="dkbm" column="dkbm"/>
        <result property="area" column="area"/>
        <result property="ydxz" column="ydxz"/>
        <result property="ydwz" column="ydwz"/>
        <result property="cjbh" column="cjbh"/>
        <result property="cjDate" column="cj_date"/>
        <result property="cjyj" column="cjyj"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_tjyydhx
        WHERE project_id = #{projectId}
    </select>

    <!-- 添加记录 -->
    <insert id="add" parameterType="com.siwei.apply.domain.Tjyydhx">
        INSERT INTO t_tjyydhx (id, project_id, dkbm, area, ydxz, ydwz,
                               cjbh, cj_date, cjyj, creator_id, created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{dkbm}, #{area}, #{ydxz}, #{ydwz},
                #{cjbh}, #{cjDate}, #{cjyj}, #{creatorId}, now(), now())
    </insert>

    <!-- 根据项目ID获取记录 -->
    <select id="get" resultMap="TjyydhxResultMap" parameterType="String">
        SELECT *
        FROM t_tjyydhx
        WHERE project_id = #{projectId}
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.TjyydhxUpdateVo">
        UPDATE t_tjyydhx
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="dkbm != null">dkbm = #{dkbm},</if>
            <if test="area != null">area = #{area},</if>
            <if test="ydxz != null">ydxz = #{ydxz},</if>
            <if test="ydwz != null">ydwz = #{ydwz},</if>
            <if test="cjbh != null">cjbh = #{cjbh},</if>
            <if test="cjDate != null">cj_date = #{cjDate},</if>
            <if test="cjyj != null">cjyj = #{cjyj},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

</mapper>
