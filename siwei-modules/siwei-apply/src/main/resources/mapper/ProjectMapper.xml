<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siwei.apply.mapper.ProjectMapper">
    <resultMap id="projectMap" type="com.siwei.apply.domain.Project">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="company" property="company"/>
        <result column="created_at" property="createAt"/>
        <result column="updated_at" property="updateAt"/>
        <result column="project_type" property="projectType"/>
        <result column="creator_id" property="creatorId"/>
    </resultMap>

    <insert id="add" parameterType="com.siwei.apply.domain.Project">
        INSERT INTO t_project (id, name, code, company,
                               created_at, updated_at, project_type, creator_id)
        VALUES (#{id}, #{name}, #{code}, #{company}, now(), now(), #{projectType},
                #{creatorId})
    </insert>

    <select id="get" resultMap="projectMap">
        SELECT *
        FROM t_project
        where t_project.id = #{id}
    </select>

    <select id="getList" parameterType="com.siwei.apply.domain.vo.ProjectFilterVo" resultMap="projectMap">
        SELECT *
        FROM t_project
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="projectType != null and projectType != 0">
                AND project_type = #{projectType}
            </if>
        </where>
        ORDER BY updated_at DESC
        LIMIT #{pageSize} offset #{offset}
    </select>
    <select id="getCount" parameterType="com.siwei.apply.domain.vo.ProjectFilterVo" resultType="int">
        SELECT COUNT(*)
        FROM t_project
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="projectType != null and projectType != 0">
                AND project_type = #{projectType}
            </if>
        </where>
    </select>
    <update id="update" parameterType="com.siwei.apply.domain.vo.ProjectUpdateVo">
        UPDATE t_project
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="company != null">company = #{company},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="batchDelete">
        DELETE FROM t_project
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
