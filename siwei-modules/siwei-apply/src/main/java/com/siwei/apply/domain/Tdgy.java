package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 土地供应对象 t_tdgy
 * 单独选址第三部分
 */
@Data
public class Tdgy {
    private String id;
    private String projectId;      // 项目ID
    private String srf;            // 土地受让人
    private String tdyt;           // 土地用途
    private String jswz;           // 建设位置
    private Float gdArea;          // 供地面积
    private String gdType;         // 供地方式
    private Boolean hasZz;         // 是否征转
    private String hbcrfapfwh;     // 用地划拨/出让方案批复文号
    private String hbcrhtbh;       // 划拨决定书/出让合同编号
    private String hbcrhtDate;       // 划拨决定书/出让合同日期
    private Map<String, Object> attachment;     // 附件
    private Boolean hasOnchain;    // 是否上链
    private String creatorId;      // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;        // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;        // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
