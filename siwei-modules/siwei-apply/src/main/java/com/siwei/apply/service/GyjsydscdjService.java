package com.siwei.apply.service;

import com.siwei.apply.domain.res.GyjsydscdjRes;
import com.siwei.apply.domain.vo.GyjsydscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydscdjVo;

/**
 * 国有建设用地使用权首次登记 服务接口
 */
public interface GyjsydscdjService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加国有建设用地使用权首次登记信息
     *
     * @param gyjsydscdjVo 国有建设用地使用权首次登记视图对象
     * @return 国有建设用地使用权首次登记ID
     */
    String add(GyjsydscdjVo gyjsydscdjVo);

    /**
     * 获取国有建设用地使用权首次登记信息
     *
     * @param projectId 项目ID
     * @return 国有建设用地使用权首次登记结果对象
     */
    GyjsydscdjRes get(String projectId);

    /**
     * 更新国有建设用地使用权首次登记信息
     *
     * @param gyjsydscdjUpdateVo 国有建设用地使用权首次登记更新视图对象
     */
    void update(GyjsydscdjUpdateVo gyjsydscdjUpdateVo);
}
