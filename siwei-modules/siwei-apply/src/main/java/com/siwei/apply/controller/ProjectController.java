package com.siwei.apply.controller;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.vo.IdsVo;
import com.siwei.apply.domain.vo.ProjectFilterVo;
import com.siwei.apply.domain.vo.ProjectUpdateVo;
import com.siwei.apply.domain.vo.ProjectVo;
import com.siwei.apply.service.ProjectService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目控制器
 */
@RestController
@RequestMapping("/project")
public class ProjectController extends BaseController {
    @Autowired
    ProjectService projectService;

    /**
     * 添加项目
     *
     * @param projectVo
     * @return
     */
    @PostMapping("")
    public R<Map> Add(@RequestBody ProjectVo projectVo) {
        try {
            String id = projectService.add(projectVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取项目
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<Project> Get(@PathVariable String id) {
        try {
            Project project = projectService.get(id);
            return R.ok(project);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取项目列表
     *
     * @return
     */
    @PostMapping("/list")
    public R<Map> GetList(@RequestBody ProjectFilterVo projectFilterVo) {
        try {
            Map<String, Object> projects = projectService.getList(projectFilterVo);
            return R.ok(projects);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新项目
     *
     * @param projectUpdateVo
     * @return
     */
    @PutMapping("")
    public R<Void> Update(@RequestBody ProjectUpdateVo projectUpdateVo) {
        try {
            projectService.update(projectUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 批量删除项目
     *
     * @param idsVo 包含要删除的项目ID列表
     * @return
     */
    @DeleteMapping("")
    public R<Void> Delete(@RequestBody IdsVo idsVo) {
        try {
            if (idsVo == null || idsVo.getIds() == null || idsVo.getIds().isEmpty()) {
                return R.fail("删除的项目ID列表不能为空");
            }
            projectService.batchDelete(idsVo.getIds());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
