package com.siwei.apply.mapper;

import com.siwei.apply.domain.Jsgcghxk;
import com.siwei.apply.domain.vo.JsgcghxkUpdateVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

// 建设工程规划许可 Mapper 接口
@Mapper
public interface JsgcghxkMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加建设工程规划许可信息
     *
     * @param jsgcghxk
     */
    void add(Jsgcghxk jsgcghxk);

    /**
     * 获取建设工程规划许可信息列表
     *
     * @param projectId
     * @return
     */
    List<Jsgcghxk> get(String projectId);

    /**
     * 更新建设工程规划许可信息
     *
     * @param jsgcghxkUpdateVo
     */
    void update(JsgcghxkUpdateVo jsgcghxkUpdateVo);
}
