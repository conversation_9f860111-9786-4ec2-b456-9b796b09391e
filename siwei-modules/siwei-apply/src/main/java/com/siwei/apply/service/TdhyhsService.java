package com.siwei.apply.service;

import com.siwei.apply.domain.res.TdhyhsRes;
import com.siwei.apply.domain.vo.TdhyhsUpdateVo;
import com.siwei.apply.domain.vo.TdhyhsVo;

/**
 * 土地核验与规划核实 服务接口
 */
public interface TdhyhsService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加土地核验与规划核实信息
     *
     * @param tdhyhsVo 土地核验与规划核实视图对象
     * @return 土地核验与规划核实ID
     */
    String add(TdhyhsVo tdhyhsVo);

    /**
     * 获取土地核验与规划核实信息
     *
     * @param projectId 项目ID
     * @return 土地核验与规划核实结果对象
     */
    TdhyhsRes get(String projectId);

    /**
     * 更新土地核验与规划核实信息
     *
     * @param tdhyhsUpdateVo 土地核验与规划核实更新视图对象
     */
    void update(TdhyhsUpdateVo tdhyhsUpdateVo);
}
