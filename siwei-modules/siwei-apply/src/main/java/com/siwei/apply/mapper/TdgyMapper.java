package com.siwei.apply.mapper;

import com.siwei.apply.domain.Tdgy;
import com.siwei.apply.domain.vo.TdgyUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 土地供应 Mapper 接口
@Mapper
public interface TdgyMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加土地供应信息
     *
     * @param tdgy
     */
    void add(Tdgy tdgy);

    /**
     * 获取土地供应信息
     *
     * @param projectId
     * @return
     */
    Tdgy get(String projectId);

    /**
     * 更新土地供应信息
     *
     * @param tdgyUpdateVo
     */
    void update(TdgyUpdateVo tdgyUpdateVo);
}
