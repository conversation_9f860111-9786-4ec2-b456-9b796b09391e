package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Jsydghxk;
import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.res.JsydghxkRes;
import com.siwei.apply.domain.vo.JsydghxkUpdateVo;
import com.siwei.apply.domain.vo.JsydghxkVo;
import com.siwei.apply.mapper.JsydghxkMapper;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.service.JsydghxkService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 建设用地规划许可 服务实现类
 */
@Service
public class JsydghxkImpl implements JsydghxkService {
    @Autowired
    private JsydghxkMapper jsydghxkMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return jsydghxkMapper.isExit(projectId);
    }

    @Override
    public String add(JsydghxkVo jsydghxkVo) {
        Jsydghxk jsydghxk = new Jsydghxk();
        BeanUtils.copyProperties(jsydghxkVo, jsydghxk);
        jsydghxk.generateId();
        jsydghxk.setCreatorId(UserId);
        jsydghxkMapper.add(jsydghxk);
        return jsydghxk.getId();
    }

    @Override
    public JsydghxkRes get(String projectId) {
        Jsydghxk jsydghxk = jsydghxkMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        JsydghxkRes jsydghxkRes = new JsydghxkRes();
        BeanUtils.copyProperties(jsydghxk, jsydghxkRes);
        jsydghxkRes.setProjectName(project.getName());
        jsydghxkRes.setProjectCode(project.getCode());
        return jsydghxkRes;
    }

    @Override
    public void update(JsydghxkUpdateVo jsydghxkUpdateVo) {
        jsydghxkMapper.update(jsydghxkUpdateVo);
    }
}