package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.vo.ProjectFilterVo;
import com.siwei.apply.domain.vo.ProjectUpdateVo;
import com.siwei.apply.domain.vo.ProjectVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.siwei.apply.common.Common.UserId;

/**
 * 项目服务实现类
 */
@Service
public class ProjectImpl implements ProjectService {
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public String add(ProjectVo projectVo) {
        Project project = new Project();
        project.generateId();
        project.setName(projectVo.getName());
        project.setCode(projectVo.getCode());
        project.setCompany(projectVo.getCompany());
        project.setProjectType(projectVo.getProjectType());
        project.setCreatorId(UserId);
        projectMapper.add(project);

        return project.getId();
    }

    @Override
    public Project get(String projectId) {
        return projectMapper.get(projectId);
    }

    @Override
    public Map<String, Object> getList(ProjectFilterVo projectFilterVo) {
        List<Project> projects = projectMapper.getList(projectFilterVo);
        Integer count = projectMapper.getCount(projectFilterVo);

        Map<String, Object> map = new HashMap<>();
        map.put("projects", projects);
        map.put("count", count);
        return map;
    }


    @Override
    public void update(ProjectUpdateVo projectUpdateVo) {
        projectMapper.update(projectUpdateVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("IDs list cannot be null or empty");
        }

        // 验证每个ID都不为空
        for (String id : ids) {
            if (id == null || id.trim().isEmpty()) {
                throw new IllegalArgumentException("ID cannot be null or empty");
            }
        }

        projectMapper.batchDelete(ids);
    }
}
