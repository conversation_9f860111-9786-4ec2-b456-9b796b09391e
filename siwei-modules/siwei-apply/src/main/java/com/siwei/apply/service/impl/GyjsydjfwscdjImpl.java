package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Gyjsydjfwscdj;
import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.res.GyjsydjfwscdjRes;
import com.siwei.apply.domain.vo.GyjsydjfwscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydjfwscdjVo;
import com.siwei.apply.mapper.GyjsydjfwscdjMapper;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.service.GyjsydjfwscdjService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 国有建设用地使用权及房屋所有权首次登记 服务实现类
 */
@Service
public class GyjsydjfwscdjImpl implements GyjsydjfwscdjService {
    @Autowired
    private GyjsydjfwscdjMapper gyjsydjfwscdjMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return gyjsydjfwscdjMapper.isExit(projectId);
    }

    @Override
    public String add(GyjsydjfwscdjVo gyjsydjfwscdjVo) {
        Gyjsydjfwscdj gyjsydjfwscdj = new Gyjsydjfwscdj();
        BeanUtils.copyProperties(gyjsydjfwscdjVo, gyjsydjfwscdj);
        gyjsydjfwscdj.generateId();
        gyjsydjfwscdj.setCreatorId(UserId);
        gyjsydjfwscdjMapper.add(gyjsydjfwscdj);
        return gyjsydjfwscdj.getId();
    }

    @Override
    public GyjsydjfwscdjRes get(String projectId) {
        Gyjsydjfwscdj gyjsydjfwscdj = gyjsydjfwscdjMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        GyjsydjfwscdjRes gyjsydjfwscdjRes = new GyjsydjfwscdjRes();
        BeanUtils.copyProperties(gyjsydjfwscdj, gyjsydjfwscdjRes);
        gyjsydjfwscdjRes.setProjectName(project.getName());
        gyjsydjfwscdjRes.setProjectCode(project.getCode());
        return gyjsydjfwscdjRes;
    }

    @Override
    public void update(GyjsydjfwscdjUpdateVo gyjsydjfwscdjUpdateVo) {
        gyjsydjfwscdjMapper.update(gyjsydjfwscdjUpdateVo);
    }
}
