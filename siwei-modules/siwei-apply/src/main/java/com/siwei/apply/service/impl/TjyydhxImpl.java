package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.Tjyydhx;
import com.siwei.apply.domain.res.TjyydhxRes;
import com.siwei.apply.domain.vo.TjyydhxUpdateVo;
import com.siwei.apply.domain.vo.TjyydhxVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.mapper.TjyydhxMapper;
import com.siwei.apply.service.TjyydhxService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 规划条件与用地红线出具 服务实现类
 */
@Service
public class TjyydhxImpl implements TjyydhxService {

    @Autowired
    private TjyydhxMapper tjyydhxMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return tjyydhxMapper.isExit(projectId);
    }

    @Override
    public String add(TjyydhxVo tjyydhxVo) {
        Tjyydhx tjyydhx = new Tjyydhx();
        BeanUtils.copyProperties(tjyydhxVo, tjyydhx);
        tjyydhx.generateId();
        tjyydhx.setCreatorId(UserId);
        tjyydhxMapper.add(tjyydhx);
        return tjyydhx.getId();
    }

    @Override
    public TjyydhxRes get(String projectId) {
        Tjyydhx tjyydhx = tjyydhxMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        TjyydhxRes tjyydhxRes = new TjyydhxRes();
        BeanUtils.copyProperties(tjyydhx, tjyydhxRes);
        tjyydhxRes.setProjectName(project.getName());
        tjyydhxRes.setProjectCode(project.getCode());
        return tjyydhxRes;
    }

    @Override
    public void update(TjyydhxUpdateVo tjyydhxUpdateVo) {
        tjyydhxMapper.update(tjyydhxUpdateVo);
    }
}
