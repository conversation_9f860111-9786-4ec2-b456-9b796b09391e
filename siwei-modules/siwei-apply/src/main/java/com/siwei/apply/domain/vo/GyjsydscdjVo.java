package com.siwei.apply.domain.vo;

import lombok.Data;

/**
 * 国有建设用地使用权首次登记 视图对象
 */
@Data
public class GyjsydscdjVo {
    private String projectId;      // 项目ID
    private String qlr;            // 权利人
    private String gyqk;           // 共有情况
    private String zl;             // 坐落
    private String qllx;           // 权利类型
    private String qlxz;           // 权利性质
    private String yt;             // 用途
    private Float area;            // 面积
    private String bdcdyh;         // 不动产单元号
    private String bdczh;          // 不动产证号
    private String djjg;           // 登记结构
    private String djDate;           // 登记日期
}
