package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 国有建设用地使用权及房屋所有权首次登记对象 t_gyjsydjfwscdj
 */
@Data
public class Gyjsydjfwscdj {
    private String id;
    private String projectId;      // 项目ID
    private String qlr;            // 权利人
    private String gyqk;           // 共有情况
    private String zl;             // 坐落
    private String qllx;           // 权利类型
    private String qlxz;           // 权利性质
    private String tdyt;           // 土地用途
    private String area;           // 面积
    private String bdcdyh;         // 不动产单元号
    private String bdcdjh;         // 不动产登记号
    private String djjg;           // 登记结构
    private String djDate;           // 登记日期
    private Map<String, Object> attachment;     // 附件
    private Boolean hasOnchain;    // 是否上链
    private String creatorId;      // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;        // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;        // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
