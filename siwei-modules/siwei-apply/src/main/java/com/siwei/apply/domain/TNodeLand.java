package com.siwei.apply.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * 记录业务流程对应的地块图形
 */
@Data
public class TNodeLand implements Serializable {
    
    /** 主键ID */
    private String id;
    
    /** 节点id */
    private String nodeId;
    
    /** 节点表名称 */
    private String nodeTable;
    
    /** 项目id */
    private String projectId;
    
    /** 对标t_geom_db_details数据 */
    private String geomDbDetailsId;
    
    /**
     * 生成主键ID
     */
    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
