package com.siwei.apply.service;

import com.siwei.apply.domain.res.GyjsydjfwscdjRes;
import com.siwei.apply.domain.vo.GyjsydjfwscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydjfwscdjVo;

/**
 * 国有建设用地使用权及房屋所有权首次登记 服务接口
 */
public interface GyjsydjfwscdjService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param gyjsydjfwscdjVo 国有建设用地使用权及房屋所有权首次登记视图对象
     * @return 国有建设用地使用权及房屋所有权首次登记ID
     */
    String add(GyjsydjfwscdjVo gyjsydjfwscdjVo);

    /**
     * 获取国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param projectId 项目ID
     * @return 国有建设用地使用权及房屋所有权首次登记结果对象
     */
    GyjsydjfwscdjRes get(String projectId);

    /**
     * 更新国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param gyjsydjfwscdjUpdateVo 国有建设用地使用权及房屋所有权首次登记更新视图对象
     */
    void update(GyjsydjfwscdjUpdateVo gyjsydjfwscdjUpdateVo);
}
