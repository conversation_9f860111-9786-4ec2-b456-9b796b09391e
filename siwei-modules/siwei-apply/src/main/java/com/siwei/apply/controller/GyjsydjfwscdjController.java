package com.siwei.apply.controller;

import com.siwei.apply.domain.res.GyjsydjfwscdjRes;
import com.siwei.apply.domain.vo.GyjsydjfwscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydjfwscdjVo;
import com.siwei.apply.service.GyjsydjfwscdjService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 国有建设用地使用权及房屋所有权首次登记 控制器
 * 单独选址第八步
 * 批次跑批第七步
 */
@RestController
@RequestMapping("/gyjsydjfwscdj")
public class GyjsydjfwscdjController extends BaseController {
    @Autowired
    private GyjsydjfwscdjService gyjsydjfwscdjService;

    /**
     * 添加国有建设用地使用权及房屋所有权首次登记
     */
    @PostMapping()
    public R<Map> Add(@RequestBody GyjsydjfwscdjVo gyjsydjfwscdjVo) {
        try {
            Boolean b = gyjsydjfwscdjService.isExit(gyjsydjfwscdjVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加国有建设用地使用权及房屋所有权首次登记");
            }
            String id = gyjsydjfwscdjService.add(gyjsydjfwscdjVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取国有建设用地使用权及房屋所有权首次登记
     *
     * @param projectId 项目ID
     * @return 国有建设用地使用权及房屋所有权首次登记
     */
    @GetMapping("/{projectId}")
    public R<GyjsydjfwscdjRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(gyjsydjfwscdjService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新国有建设用地使用权及房屋所有权首次登记
     *
     * @param gyjsydjfwscdjUpdateVo 国有建设用地使用权及房屋所有权首次登记
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody GyjsydjfwscdjUpdateVo gyjsydjfwscdjUpdateVo) {
        try {
            gyjsydjfwscdjService.update(gyjsydjfwscdjUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
