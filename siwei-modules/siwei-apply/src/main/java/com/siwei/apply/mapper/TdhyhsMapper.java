package com.siwei.apply.mapper;

import com.siwei.apply.domain.Tdhyhs;
import com.siwei.apply.domain.vo.TdhyhsUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 土地核验与规划核实 Mapper 接口
@Mapper
public interface TdhyhsMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加土地核验与规划核实信息
     *
     * @param tdhyhs
     */
    void add(Tdhyhs tdhyhs);

    /**
     * 获取土地核验与规划核实信息
     *
     * @param projectId
     * @return
     */
    Tdhyhs get(String projectId);

    /**
     * 更新土地核验与规划核实信息
     *
     * @param tdhyhsUpdateVo
     */
    void update(TdhyhsUpdateVo tdhyhsUpdateVo);
}
