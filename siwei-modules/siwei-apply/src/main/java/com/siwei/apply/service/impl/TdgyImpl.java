package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.Tdgy;
import com.siwei.apply.domain.res.TdgyRes;
import com.siwei.apply.domain.vo.TdgyUpdateVo;
import com.siwei.apply.domain.vo.TdgyVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.mapper.TdgyMapper;
import com.siwei.apply.service.TdgyService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 土地供应 服务实现类
 */
@Service
public class TdgyImpl implements TdgyService {
    @Autowired
    private TdgyMapper tdgyMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return tdgyMapper.isExit(projectId);
    }

    @Override
    public String add(TdgyVo tdgyVo) {
        Tdgy tdgy = new Tdgy();
        BeanUtils.copyProperties(tdgyVo, tdgy);
        tdgy.generateId();
        tdgy.setCreatorId(UserId);
        tdgyMapper.add(tdgy);
        return tdgy.getId();
    }

    @Override
    public TdgyRes get(String projectId) {
        Tdgy tdgy = tdgyMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        TdgyRes tdgyRes = new TdgyRes();
        BeanUtils.copyProperties(tdgy, tdgyRes);
        tdgyRes.setProjectName(project.getName());
        tdgyRes.setProjectCode(project.getCode());
        return tdgyRes;
    }

    @Override
    public void update(TdgyUpdateVo tdgyUpdateVo) {
        tdgyMapper.update(tdgyUpdateVo);
    }
}