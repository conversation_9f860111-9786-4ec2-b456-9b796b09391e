package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 建设用地规划许可对象 t_jsydghxk
 */
@Data
public class Jsydghxk {
    private String id;                 // 主键
    private String projectId;          // 项目ID
    private String yddw;               // 用地单位
    private String ydwz;               // 用地位置
    private Float ydArea;              // 用地面积
    private String tdyt;               // 土地用途
    private String tdhqfs;             // 土地获取方式
    private String pzydjg;             // 批准用地机关
    private String pzydwh;             // 批准用地文号
    private String jsgm;               // 建设规模
    private String zsbh;               // 证书编号
    private String fzjg;               // 发证机关
    private String fzDate;               // 发证日期
    private Map<String, Object> attachment; // 附件
    private Boolean hasOnchain;        // 是否上链
    private String creatorId;          // 创建人ID
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;            // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;            // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
