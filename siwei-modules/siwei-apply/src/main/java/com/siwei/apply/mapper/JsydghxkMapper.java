package com.siwei.apply.mapper;

import com.siwei.apply.domain.Jsydghxk;
import com.siwei.apply.domain.vo.JsydghxkUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 建设用地规划许可 Mapper 接口
@Mapper
public interface JsydghxkMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加建设用地规划许可信息
     *
     * @param jsydghxk
     */
    void add(Jsydghxk jsydghxk);

    /**
     * 获取建设用地规划许可信息
     *
     * @param projectId
     * @return
     */
    Jsydghxk get(String projectId);

    /**
     * 更新建设用地规划许可信息
     *
     * @param jsydghxkUpdateVo
     */
    void update(JsydghxkUpdateVo jsydghxkUpdateVo);
}
