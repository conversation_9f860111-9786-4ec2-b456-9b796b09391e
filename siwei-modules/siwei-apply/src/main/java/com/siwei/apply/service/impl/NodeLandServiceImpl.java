package com.siwei.apply.service.impl;

import com.siwei.apply.mapper.NodeLandMapper;
import com.siwei.apply.service.NodeLandService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 节点地块关联 服务实现类
 */
@Service
public class NodeLandServiceImpl implements NodeLandService {
    
    private static final Logger logger = LoggerFactory.getLogger(NodeLandServiceImpl.class);
    
    @Autowired
    private NodeLandMapper nodeLandMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createNodeLand(String nodeId, String geomDbId) {
        try {
            if (nodeId == null || nodeId.trim().isEmpty()) {
                logger.warn("创建节点地块关联失败：nodeId不能为空");
                return false;
            }

            if (geomDbId == null || geomDbId.trim().isEmpty()) {
                logger.warn("创建节点地块关联失败：geomDbId不能为空");
                return false;
            }

            // 先删除该nodeId的所有关联记录
            int deletedCount = nodeLandMapper.deleteByNodeId(nodeId);
            logger.info("删除了 {} 条已存在的节点地块关联记录，nodeId: {}", deletedCount, nodeId);

            // 创建新的关联记录
            int result = nodeLandMapper.insertByNodeIdAndGeomDbId(nodeId, geomDbId);

            if (result > 0) {
                logger.info("成功创建节点地块关联记录，nodeId: {}, geomDbId: {}", nodeId, geomDbId);
                return true;
            } else {
                logger.warn("创建节点地块关联记录失败，nodeId: {}, geomDbId: {}", nodeId, geomDbId);
                return false;
            }

        } catch (Exception e) {
            logger.error("创建节点地块关联记录异常，nodeId: {}, geomDbId: {}", nodeId, geomDbId, e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }
    
    @Override
    public List<String> getGeomByNodeId(String nodeId) {
        try {
            if (nodeId == null || nodeId.trim().isEmpty()) {
                logger.warn("查询地块几何信息失败：nodeId不能为空");
                return null;
            }

            List<String> geomList = nodeLandMapper.selectGeomByNodeId(nodeId);
            logger.info("查询到 {} 条地块几何信息，nodeId: {}", geomList.size(), nodeId);
            return geomList;

        } catch (Exception e) {
            logger.error("查询地块几何信息异常，nodeId: {}", nodeId, e);
            return null;
        }
    }
    
    @Override
    public int deleteByNodeId(String nodeId) {
        try {
            if (nodeId == null || nodeId.trim().isEmpty()) {
                logger.warn("删除节点地块关联失败：nodeId不能为空");
                return 0;
            }
            
            int deletedCount = nodeLandMapper.deleteByNodeId(nodeId);
            logger.info("删除了 {} 条节点地块关联记录，nodeId: {}", deletedCount, nodeId);
            return deletedCount;
            
        } catch (Exception e) {
            logger.error("删除节点地块关联记录异常，nodeId: {}", nodeId, e);
            return 0;
        }
    }
}
