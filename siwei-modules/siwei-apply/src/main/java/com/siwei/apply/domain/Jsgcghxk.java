package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 建设工程规划许可对象 t_jsgcghxk
 */
@Data
public class Jsgcghxk {
    private String id; // 主键
    private String projectId; // 项目ID
    private String ydwz; // 用地位置
    private Integer qs; // 期数
    private Integer djq; // 第几期
    private String jsgm; // 建设规模
    private String zsbh; // 证书编号
    private String fzjg; // 发证机关

    private String fzDate; // 发证日期
    private String attachment; // 附件
    private Boolean hasOnchain; // 是否上链
    private String creatorId; // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt; // 更新时间

    public void generateId() {
        this.id = java.util.UUID.randomUUID().toString();
    }
}
