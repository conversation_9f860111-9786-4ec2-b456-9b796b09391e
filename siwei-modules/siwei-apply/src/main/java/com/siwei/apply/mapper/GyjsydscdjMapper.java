package com.siwei.apply.mapper;

import com.siwei.apply.domain.Gyjsydscdj;
import com.siwei.apply.domain.vo.GyjsydscdjUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 国有建设用地使用权首次登记 Mapper 接口
@Mapper
public interface GyjsydscdjMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加国有建设用地使用权首次登记信息
     *
     * @param gyjsydscdj
     */
    void add(Gyjsydscdj gyjsydscdj);

    /**
     * 获取国有建设用地使用权首次登记信息
     *
     * @param projectId
     * @return
     */
    Gyjsydscdj get(String projectId);

    /**
     * 更新国有建设用地使用权首次登记信息
     *
     * @param gyjsydscdjUpdateVo
     */
    void update(GyjsydscdjUpdateVo gyjsydscdjUpdateVo);
}
