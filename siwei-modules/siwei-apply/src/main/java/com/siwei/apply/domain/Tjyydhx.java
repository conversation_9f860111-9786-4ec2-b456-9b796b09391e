package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * 规划条件与用地红线出具对象 tjyydhx
 */
@Data
public class Tjyydhx {
    private String id;
    private String projectId;      // 项目ID
    private String dkbm;           // 地块编码
    private Float area;            // 用地面积
    private String ydxz;           // 用地性质
    private String ydwz;           // 用地位置
    private String cjbh;           // 出具编号
    private String cjDate;           // 出具日期
    private String cjyj;           // 出具意见
    private String creatorId;      // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;        // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;        // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
