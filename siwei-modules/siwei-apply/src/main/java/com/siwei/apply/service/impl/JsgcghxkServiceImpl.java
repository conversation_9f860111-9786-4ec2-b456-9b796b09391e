package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Jsgcghxk;
import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.res.JsgcghxkRes;
import com.siwei.apply.domain.vo.JsgcghxkUpdateVo;
import com.siwei.apply.domain.vo.JsgcghxkVo;
import com.siwei.apply.mapper.JsgcghxkMapper;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.service.JsgcghxkService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.siwei.apply.common.Common.UserId;

/**
 * 建设工程规划许可 服务实现类
 */
@Service
public class JsgcghxkServiceImpl implements JsgcghxkService {
    @Autowired
    private JsgcghxkMapper jsgcghxkMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return jsgcghxkMapper.isExit(projectId);
    }

    @Override
    public String add(JsgcghxkVo jsgcghxkVo) {
        Jsgcghxk jsgcghxk = new Jsgcghxk();
        BeanUtils.copyProperties(jsgcghxkVo, jsgcghxk);
        jsgcghxk.generateId();
        jsgcghxk.setCreatorId(UserId);
        jsgcghxkMapper.add(jsgcghxk);
        return jsgcghxk.getId();
    }

    @Override
    public List<JsgcghxkRes> get(String projectId) {
        List<Jsgcghxk> jsgcghxkList = jsgcghxkMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        List<JsgcghxkRes> jsgcghxkResList = new ArrayList<>();
        for (Jsgcghxk jsgcghxk : jsgcghxkList) {
            JsgcghxkRes jsgcghxkRes = new JsgcghxkRes();
            BeanUtils.copyProperties(jsgcghxk, jsgcghxkRes);
            jsgcghxkRes.setProjectName(project.getName());
            jsgcghxkRes.setProjectCode(project.getCode());
            jsgcghxkResList.add(jsgcghxkRes);
        }
        return jsgcghxkResList;
    }

    @Override
    public void update(JsgcghxkUpdateVo jsgcghxkUpdateVo) {
        jsgcghxkMapper.update(jsgcghxkUpdateVo);
    }
}
