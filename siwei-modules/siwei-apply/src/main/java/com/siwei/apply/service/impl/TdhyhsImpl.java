package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.Tdhyhs;
import com.siwei.apply.domain.res.TdhyhsRes;
import com.siwei.apply.domain.vo.TdhyhsUpdateVo;
import com.siwei.apply.domain.vo.TdhyhsVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.mapper.TdhyhsMapper;
import com.siwei.apply.service.TdhyhsService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 土地核验与规划核实 服务实现类
 */
@Service
public class TdhyhsImpl implements TdhyhsService {
    @Autowired
    private TdhyhsMapper tdhyhsMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return tdhyhsMapper.isExit(projectId);
    }

    @Override
    public String add(TdhyhsVo tdhyhsVo) {
        Tdhyhs tdhyhs = new Tdhyhs();
        BeanUtils.copyProperties(tdhyhsVo, tdhyhs);
        tdhyhs.generateId();
        tdhyhs.setCreatorId(UserId);
        tdhyhsMapper.add(tdhyhs);
        return tdhyhs.getId();
    }

    @Override
    public TdhyhsRes get(String projectId) {
        Tdhyhs tdhyhs = tdhyhsMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        TdhyhsRes tdhyhsRes = new TdhyhsRes();
        BeanUtils.copyProperties(tdhyhs, tdhyhsRes);
        tdhyhsRes.setProjectName(project.getName());
        tdhyhsRes.setProjectCode(project.getCode());
        return tdhyhsRes;
    }

    @Override
    public void update(TdhyhsUpdateVo tdhyhsUpdateVo) {
        tdhyhsMapper.update(tdhyhsUpdateVo);
    }
}
