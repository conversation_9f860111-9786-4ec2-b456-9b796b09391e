package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Gyjsydscdj;
import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.res.GyjsydscdjRes;
import com.siwei.apply.domain.vo.GyjsydscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydscdjVo;
import com.siwei.apply.mapper.GyjsydscdjMapper;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.service.GyjsydscdjService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 国有建设用地使用权首次登记 服务实现类
 */
@Service
public class GyjsydscdjImpl implements GyjsydscdjService {
    @Autowired
    private GyjsydscdjMapper gyjsydscdjMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return gyjsydscdjMapper.isExit(projectId);
    }

    @Override
    public String add(GyjsydscdjVo gyjsydscdjVo) {
        Gyjsydscdj gyjsydscdj = new Gyjsydscdj();
        BeanUtils.copyProperties(gyjsydscdjVo, gyjsydscdj);
        gyjsydscdj.generateId();
        gyjsydscdj.setCreatorId(UserId);
        gyjsydscdjMapper.add(gyjsydscdj);
        return gyjsydscdj.getId();
    }

    @Override
    public GyjsydscdjRes get(String projectId) {
        Gyjsydscdj gyjsydscdj = gyjsydscdjMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        GyjsydscdjRes gyjsydscdjRes = new GyjsydscdjRes();
        BeanUtils.copyProperties(gyjsydscdj, gyjsydscdjRes);
        gyjsydscdjRes.setProjectName(project.getName());
        gyjsydscdjRes.setProjectCode(project.getCode());
        return gyjsydscdjRes;
    }

    @Override
    public void update(GyjsydscdjUpdateVo gyjsydscdjUpdateVo) {
        gyjsydscdjMapper.update(gyjsydscdjUpdateVo);
    }
}