package com.siwei.apply.service;

import com.siwei.apply.domain.res.TjyydhxRes;
import com.siwei.apply.domain.vo.TjyydhxUpdateVo;
import com.siwei.apply.domain.vo.TjyydhxVo;

/**
 * 规划条件与用地红线出具 服务接口
 */
public interface TjyydhxService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加规划条件与用地红线出具信息
     *
     * @param tjyydhxVo 规划条件与用地红线出具视图对象
     * @return 规划条件与用地红线出具ID
     */
    String add(TjyydhxVo tjyydhxVo);

    /**
     * 获取规划条件与用地红线出具信息
     *
     * @param projectId 项目ID
     * @return 规划条件与用地红线出具结果对象
     */
    TjyydhxRes get(String projectId);

    /**
     * 更新规划条件与用地红线出具信息
     *
     * @param tjyydhxUpdateVo 规划条件与用地红线出具更新视图对象
     */
    void update(TjyydhxUpdateVo tjyydhxUpdateVo);
}
