package com.siwei.apply.service;

import com.siwei.apply.domain.res.JsgcghxkRes;
import com.siwei.apply.domain.vo.JsgcghxkUpdateVo;
import com.siwei.apply.domain.vo.JsgcghxkVo;

import java.util.List;

/**
 * 建设工程规划许可 服务接口
 */
public interface JsgcghxkService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加建设工程规划许可信息
     *
     * @param jsgcghxkVo 建设工程规划许可视图对象
     * @return 建设工程规划许可ID
     */
    String add(JsgcghxkVo jsgcghxkVo);

    /**
     * 获取建设工程规划许可信息列表
     *
     * @param projectId 项目ID
     * @return 建设工程规划许可结果对象列表
     */
    List<JsgcghxkRes> get(String projectId);

    /**
     * 更新建设工程规划许可信息
     *
     * @param jsgcghxkUpdateVo 建设工程规划许可更新视图对象
     */
    void update(JsgcghxkUpdateVo jsgcghxkUpdateVo);
}
