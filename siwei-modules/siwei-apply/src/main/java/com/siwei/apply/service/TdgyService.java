package com.siwei.apply.service;

import com.siwei.apply.domain.res.TdgyRes;
import com.siwei.apply.domain.vo.TdgyUpdateVo;
import com.siwei.apply.domain.vo.TdgyVo;

/**
 * 土地供应 服务接口
 */
public interface TdgyService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加土地供应信息
     *
     * @param tdgyVo 土地供应视图对象
     * @return 土地供应ID
     */
    String add(TdgyVo tdgyVo);

    /**
     * 获取土地供应信息
     *
     * @param projectId 项目ID
     * @return 土地供应结果对象
     */
    TdgyRes get(String projectId);

    /**
     * 更新土地供应信息
     *
     * @param tdgyUpdateVo 土地供应更新视图对象
     */
    void update(TdgyUpdateVo tdgyUpdateVo);
}
