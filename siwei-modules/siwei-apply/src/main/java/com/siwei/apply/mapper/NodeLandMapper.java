package com.siwei.apply.mapper;

import com.siwei.apply.domain.NodeLand;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 记录业务流程对应的地块图形 Mapper 接口
 */
@Mapper
public interface NodeLandMapper {

    /**
     * 根据node_id查询地块几何信息，返回ewkt格式
     *
     * @param nodeId 节点ID
     * @return 包含nodeId和geom字段的Map列表，key和value都是String类型
     */
    List<Map<String, String>> selectGeomByNodeId(String nodeId);

    /**
     * 根据node_id和geom_db_id创建记录
     *
     * @param nodeId 节点ID
     * @param geomDbId 几何数据库ID
     * @return 影响行数
     */
    int insertByNodeIdAndGeomDbId(String nodeId, String geomDbId);

    /**
     * 根据node_id删除记录
     *
     * @param nodeId 节点ID
     * @return 影响行数
     */
    int deleteByNodeId(String nodeId);

}
