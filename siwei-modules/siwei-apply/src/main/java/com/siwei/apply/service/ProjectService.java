package com.siwei.apply.service;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.vo.ProjectFilterVo;
import com.siwei.apply.domain.vo.ProjectUpdateVo;
import com.siwei.apply.domain.vo.ProjectVo;

import java.util.List;
import java.util.Map;

/**
 * 项目服务接口
 */
public interface ProjectService {
    /**
     * 添加项目
     *
     * @param projectVo
     * @return 项目ID
     */
    String add(ProjectVo projectVo);

    /**
     * 获取项目
     *
     * @param projectId
     * @return 项目对象
     */
    Project get(String projectId);

    /**
     * 获取项目列表
     *
     * @param projectFilterVo
     * @return 项目列表
     */
    Map<String, Object> getList(ProjectFilterVo projectFilterVo);


    /**
     * 更新项目
     *
     * @param projectUpdateVo
     * @return
     */
    void update(ProjectUpdateVo projectUpdateVo);

    /**
     * 批量删除项目
     *
     * @param ids 项目ID列表
     */
    void batchDelete(List<String> ids);
}
