package com.siwei.apply.service;

import java.util.List;
import java.util.Map;

/**
 * 节点地块关联 服务接口
 */
public interface NodeLandService {
    
    /**
     * 根据nodeId和geomDbId创建节点地块关联记录
     *
     * @param nodeId 节点ID
     * @param geomDbId 几何数据库ID
     * @return 是否创建成功
     */
    boolean createNodeLand(String nodeId, String geomDbId);
    
    /**
     * 根据nodeId查询地块几何信息，返回ewkt格式
     *
     * @param nodeId 节点ID
     * @return geom字符串数组
     */
    List<String> getGeomByNodeId(String nodeId);
    
    /**
     * 根据nodeId删除节点地块关联记录
     *
     * @param nodeId 节点ID
     * @return 删除的记录数
     */
    int deleteByNodeId(String nodeId);
}
