package com.siwei.apply.domain.vo;

import lombok.Data;

/**
 * 建设用地规划许可 视图对象
 */
@Data
public class JsydghxkVo {
    private String projectId;          // 项目ID
    private String yddw;               // 用地单位
    private String ydwz;               // 用地位置
    private Float ydArea;              // 用地面积
    private String tdyt;               // 土地用途
    private String tdhqfs;             // 土地获取方式
    private String pzydjg;             // 批准用地机关
    private String pzydwh;             // 批准用地文号
    private String jsgm;               // 建设规模
    private String zsbh;               // 证书编号
    private String fzjg;               // 发证机关
    private String fzDate;               // 发证日期
}
