package com.siwei.apply.controller;

import com.siwei.apply.domain.res.TdgyRes;
import com.siwei.apply.domain.vo.TdgyUpdateVo;
import com.siwei.apply.domain.vo.TdgyVo;
import com.siwei.apply.service.TdgyService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 土地供应 控制器
 * 单独选址第三步
 * 批次跑批第二步
 */
@RestController
@RequestMapping("/tdgy")
public class TdgyController extends BaseController {
    @Autowired
    private TdgyService tdgyService;

    /**
     * 添加土地供应
     */
    @PostMapping()
    public R<Map> Add(@RequestBody TdgyVo tdgyVo) {
        try {
            Boolean b = tdgyService.isExit(tdgyVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加土地供应");
            }
            String id = tdgyService.add(tdgyVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取土地供应
     *
     * @param projectId 项目ID
     * @return 土地供应
     */
    @GetMapping("/{projectId}")
    public R<TdgyRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(tdgyService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新土地供应
     *
     * @param tdgyUpdateVo 土地供应
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody TdgyUpdateVo tdgyUpdateVo) {
        try {
            tdgyService.update(tdgyUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
