package com.siwei.apply.service;

import com.siwei.apply.domain.res.JsydghxkRes;
import com.siwei.apply.domain.vo.JsydghxkUpdateVo;
import com.siwei.apply.domain.vo.JsydghxkVo;

/**
 * 建设用地规划许可 服务接口
 */
public interface JsydghxkService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加建设用地规划许可信息
     *
     * @param jsydghxkVo 建设用地规划许可视图对象
     * @return 建设用地规划许可ID
     */
    String add(JsydghxkVo jsydghxkVo);

    /**
     * 获取建设用地规划许可信息
     *
     * @param projectId 项目ID
     * @return 建设用地规划许可结果对象
     */
    JsydghxkRes get(String projectId);

    /**
     * 更新建设用地规划许可信息
     *
     * @param jsydghxkUpdateVo 建设用地规划许可更新视图对象
     */
    void update(JsydghxkUpdateVo jsydghxkUpdateVo);
}
