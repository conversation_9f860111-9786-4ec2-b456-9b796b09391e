package com.siwei.apply.controller;

import com.siwei.apply.domain.res.GyjsydscdjRes;
import com.siwei.apply.domain.vo.GyjsydscdjUpdateVo;
import com.siwei.apply.domain.vo.GyjsydscdjVo;
import com.siwei.apply.service.GyjsydscdjService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 国有建设用地使用权首次登记 控制器
 * 单独选址第四步
 * 批次跑批第三步
 */
@RestController
@RequestMapping("/gyjsydscdj")
public class GyjsydscdjController extends BaseController {
    @Autowired
    private GyjsydscdjService gyjsydscdjService;

    /**
     * 添加国有建设用地使用权首次登记
     */
    @PostMapping()
    public R<Map> Add(@RequestBody GyjsydscdjVo gyjsydscdjVo) {
        try {
            Boolean b = gyjsydscdjService.isExit(gyjsydscdjVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加国有建设用地使用权首次登记");
            }
            String id = gyjsydscdjService.add(gyjsydscdjVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取国有建设用地使用权首次登记
     *
     * @param projectId 项目ID
     * @return 国有建设用地使用权首次登记
     */
    @GetMapping("/{projectId}")
    public R<GyjsydscdjRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(gyjsydscdjService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新国有建设用地使用权首次登记
     *
     * @param gyjsydscdjUpdateVo 国有建设用地使用权首次登记
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody GyjsydscdjUpdateVo gyjsydscdjUpdateVo) {
        try {
            gyjsydscdjService.update(gyjsydscdjUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}