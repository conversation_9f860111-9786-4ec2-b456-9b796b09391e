package com.siwei.apply.mapper;

import com.siwei.apply.domain.Gyjsydjfwscdj;
import com.siwei.apply.domain.vo.GyjsydjfwscdjUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 国有建设用地使用权及房屋所有权首次登记 Mapper 接口
@Mapper
public interface GyjsydjfwscdjMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param gyjsydjfwscdj
     */
    void add(Gyjsydjfwscdj gyjsydjfwscdj);

    /**
     * 获取国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param projectId
     * @return
     */
    Gyjsydjfwscdj get(String projectId);

    /**
     * 更新国有建设用地使用权及房屋所有权首次登记信息
     *
     * @param gyjsydjfwscdjUpdateVo
     */
    void update(GyjsydjfwscdjUpdateVo gyjsydjfwscdjUpdateVo);
}
