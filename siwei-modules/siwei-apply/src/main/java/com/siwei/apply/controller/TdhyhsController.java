package com.siwei.apply.controller;

import com.siwei.apply.domain.res.TdhyhsRes;
import com.siwei.apply.domain.vo.TdhyhsUpdateVo;
import com.siwei.apply.domain.vo.TdhyhsVo;
import com.siwei.apply.service.TdhyhsService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 土地核验与规划核实 控制器
 * 单独选址第七步
 * 批次跑批第六步
 */
@RestController
@RequestMapping("/tdhyhs")
public class TdhyhsController extends BaseController {
    @Autowired
    private TdhyhsService tdhyhsService;

    /**
     * 添加土地核验与规划核实
     */
    @PostMapping()
    public R<Map> Add(@RequestBody TdhyhsVo tdhyhsVo) {
        try {
            Boolean b = tdhyhsService.isExit(tdhyhsVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加土地核验与规划核实");
            }
            String id = tdhyhsService.add(tdhyhsVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取土地核验与规划核实
     *
     * @param projectId 项目ID
     * @return 土地核验与规划核实
     */
    @GetMapping("/{projectId}")
    public R<TdhyhsRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(tdhyhsService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新土地核验与规划核实
     *
     * @param tdhyhsUpdateVo 土地核验与规划核实
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody TdhyhsUpdateVo tdhyhsUpdateVo) {
        try {
            tdhyhsService.update(tdhyhsUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
