package com.siwei.apply.controller;

import com.siwei.apply.domain.res.TjyydhxRes;
import com.siwei.apply.domain.vo.TjyydhxUpdateVo;
import com.siwei.apply.domain.vo.TjyydhxVo;
import com.siwei.apply.service.TjyydhxService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 规划条件与用地红线出具 控制器
 * 批次报批第一步
 */
@RestController
@RequestMapping("/tjyydhx")
public class TjyydhxController extends BaseController {
    @Autowired
    private TjyydhxService tjyydhxService;

    /**
     * 添加规划条件与用地红线出具
     */
    @PostMapping()
    public R<Map> Add(@RequestBody TjyydhxVo tjyydhxVo) {
        try {
            Boolean b = tjyydhxService.isExit(tjyydhxVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加规划条件与用地红线出具");
            }
            String id = tjyydhxService.add(tjyydhxVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取规划条件与用地红线出具
     *
     * @param projectId 项目ID
     * @return 规划条件与用地红线出具
     */
    @GetMapping("/{projectId}")
    public R<TjyydhxRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(tjyydhxService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新规划条件与用地红线出具
     *
     * @param tjyydhxUpdateVo 规划条件与用地红线出具
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody TjyydhxUpdateVo tjyydhxUpdateVo) {
        try {
            tjyydhxService.update(tjyydhxUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
