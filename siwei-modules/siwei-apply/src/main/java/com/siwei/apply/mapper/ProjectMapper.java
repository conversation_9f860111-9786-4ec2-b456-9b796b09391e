package com.siwei.apply.mapper;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.vo.ProjectFilterVo;
import com.siwei.apply.domain.vo.ProjectUpdateVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProjectMapper {
    /**
     * 创建项目
     *
     * @param project
     */
    void add(Project project);

    /**
     * 获取项目
     */
    Project get(String id);

    /**
     * 获取项目列表
     */
    List<Project> getList(ProjectFilterVo projectFilterVo);

    /**
     * 获取项目总数
     *
     * @param projectFilterVo
     * @return
     */
    Integer getCount(ProjectFilterVo projectFilterVo);

    /**
     * 更新项目
     *
     * @param projectUpdateVo
     */
    void update(ProjectUpdateVo projectUpdateVo);

    /**
     * 批量删除项目
     *
     * @param ids 项目ID列表
     */
    void batchDelete(@Param("ids") List<String> ids);
}