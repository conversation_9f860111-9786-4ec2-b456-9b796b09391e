package com.siwei.apply.mapper;

import com.siwei.apply.domain.TNodeLand;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 记录业务流程对应的地块图形 Mapper 接口
 */
@Mapper
public interface TNodeLandMapper {
    
    /**
     * 根据ID查询记录
     *
     * @param id 主键ID
     * @return TNodeLand
     */
    TNodeLand selectById(String id);
    
    /**
     * 根据项目ID查询记录列表
     *
     * @param projectId 项目ID
     * @return TNodeLand列表
     */
    List<TNodeLand> selectByProjectId(String projectId);
    
    /**
     * 根据节点ID和节点表名查询记录
     *
     * @param nodeId 节点ID
     * @param nodeTable 节点表名
     * @return TNodeLand列表
     */
    List<TNodeLand> selectByNodeIdAndTable(String nodeId, String nodeTable);
    
    /**
     * 插入记录
     *
     * @param tNodeLand 记录对象
     * @return 影响行数
     */
    int insert(TNodeLand tNodeLand);
    
    /**
     * 更新记录
     *
     * @param tNodeLand 记录对象
     * @return 影响行数
     */
    int update(TNodeLand tNodeLand);
    
    /**
     * 根据ID删除记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(String id);
    
    /**
     * 根据项目ID删除记录
     *
     * @param projectId 项目ID
     * @return 影响行数
     */
    int deleteByProjectId(String projectId);
}
