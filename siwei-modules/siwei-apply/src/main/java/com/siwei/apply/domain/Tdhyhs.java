package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 土地核验与规划核实
 */
@Data
public class Tdhyhs implements Serializable {
    private String id;                  // 主键ID
    private String projectId;           // 项目ID
    private String ydwz;                // 用地位置
    private String hgzh;                // 合格证号
    private String fzjg;                // 发证机关

    private String fzDate;                // 发证日期

    private Map<String, Object> attachment; // 附件
    private Boolean hasOnchain;         // 是否上链
    private String creatorId;           // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;             // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;             // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
