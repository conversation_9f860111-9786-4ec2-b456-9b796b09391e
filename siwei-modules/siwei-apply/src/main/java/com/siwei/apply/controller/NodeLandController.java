package com.siwei.apply.controller;

import com.siwei.apply.mapper.NodeLandMapper;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 节点地块关联控制器
 */
@RestController
@RequestMapping("/nodeland")
public class NodeLandController extends BaseController {
    
    @Autowired
    private NodeLandMapper nodeLandMapper;

    /**
     * 根据nodeId和geomDbId创建节点地块关联记录
     *
     * @param requestBody 包含nodeId和geomDbId的请求体
     * @return 创建结果
     */
    @PostMapping("/create")
    public R<Map<String, Object>> createNodeLand(@RequestBody Map<String, String> requestBody) {
        try {
            String nodeId = requestBody.get("nodeId");
            String geomDbId = requestBody.get("geomDbId");
            
            if (nodeId == null || nodeId.trim().isEmpty()) {
                return R.fail("nodeId不能为空");
            }
            
            if (geomDbId == null || geomDbId.trim().isEmpty()) {
                return R.fail("geomDbId不能为空");
            }
            
            int result = nodeLandMapper.insertByNodeIdAndGeomDbId(nodeId, geomDbId);
            
            if (result > 0) {
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("nodeId", nodeId);
                responseData.put("geomDbId", geomDbId);
                responseData.put("success", true);
                return R.ok(responseData);
            } else {
                return R.fail("创建节点地块关联记录失败");
            }
            
        } catch (Exception e) {
            logger.error("创建节点地块关联记录异常", e);
            return R.fail("创建节点地块关联记录异常：" + e.getMessage());
        }
    }

    /**
     * 根据nodeId查询地块几何信息
     *
     * @param nodeId 节点ID
     * @return 地块几何信息列表
     */
    @GetMapping("/geom/{nodeId}")
    public R<List<Map<String, String>>> getGeomByNodeId(@PathVariable String nodeId) {
        try {
            if (nodeId == null || nodeId.trim().isEmpty()) {
                return R.fail("nodeId不能为空");
            }
            
            List<Map<String, String>> geomList = nodeLandMapper.selectGeomByNodeId(nodeId);
            return R.ok(geomList);
            
        } catch (Exception e) {
            logger.error("查询节点地块几何信息异常", e);
            return R.fail("查询节点地块几何信息异常：" + e.getMessage());
        }
    }

    /**
     * 根据nodeId删除节点地块关联记录
     *
     * @param nodeId 节点ID
     * @return 删除结果
     */
    @DeleteMapping("/{nodeId}")
    public R<Map<String, Object>> deleteByNodeId(@PathVariable String nodeId) {
        try {
            if (nodeId == null || nodeId.trim().isEmpty()) {
                return R.fail("nodeId不能为空");
            }
            
            int deletedCount = nodeLandMapper.deleteByNodeId(nodeId);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("nodeId", nodeId);
            responseData.put("deletedCount", deletedCount);
            responseData.put("success", deletedCount > 0);
            
            return R.ok(responseData);
            
        } catch (Exception e) {
            logger.error("删除节点地块关联记录异常", e);
            return R.fail("删除节点地块关联记录异常：" + e.getMessage());
        }
    }
}
