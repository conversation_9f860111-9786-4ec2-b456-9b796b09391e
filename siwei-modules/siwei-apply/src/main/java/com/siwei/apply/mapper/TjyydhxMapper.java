package com.siwei.apply.mapper;

import com.siwei.apply.domain.Tjyydhx;
import com.siwei.apply.domain.vo.TjyydhxUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 规划条件与用地红线出具 Mapper 接口
@Mapper
public interface TjyydhxMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加规划条件与用地红线出具信息
     *
     * @param tjyydhx
     */
    void add(Tjyydhx tjyydhx);

    /**
     * 获取规划条件与用地红线出具信息
     *
     * @param projectId
     * @return
     */
    Tjyydhx get(String projectId);

    /**
     * 更新规划条件与用地红线出具信息
     *
     * @param tjyydhxUpdateVo
     */
    void update(TjyydhxUpdateVo tjyydhxUpdateVo);
}
