package com.siwei.apply.controller;

import com.siwei.apply.domain.res.JsydghxkRes;
import com.siwei.apply.domain.vo.JsydghxkUpdateVo;
import com.siwei.apply.domain.vo.JsydghxkVo;
import com.siwei.apply.service.JsydghxkService;
import com.siwei.common.core.domain.R;
import com.siwei.common.core.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 建设用地规划许可 控制器
 * 单独选址第五步
 * 批次报批第四步
 */
@RestController
@RequestMapping("/jsydghxk")
public class JsydghxkController extends BaseController {
    @Autowired
    private JsydghxkService jsydghxkService;

    /**
     * 添加建设用地规划许可
     */
    @PostMapping()
    public R<Map> Add(@RequestBody JsydghxkVo jsydghxkVo) {
        try {
            Boolean b = jsydghxkService.isExit(jsydghxkVo.getProjectId());
            if (b == true) {
                return R.fail("此项目已添加建设用地规划许可");
            }
            String id = jsydghxkService.add(jsydghxkVo);
            Map<String, String> map = new HashMap<>();
            map.put("id", id);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取建设用地规划许可
     *
     * @param projectId 项目ID
     * @return 建设用地规划许可
     */
    @GetMapping("/{projectId}")
    public R<JsydghxkRes> Get(@PathVariable String projectId) {
        try {
            return R.ok(jsydghxkService.get(projectId));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新建设用地规划许可
     *
     * @param jsydghxkUpdateVo 建设用地规划许可
     * @return 操作结果
     */
    @PutMapping()
    public R<Void> Update(@RequestBody JsydghxkUpdateVo jsydghxkUpdateVo) {
        try {
            jsydghxkService.update(jsydghxkUpdateVo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
